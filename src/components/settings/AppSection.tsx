import { scale } from "@/src/_helper/Scaler";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { Href, useRouter } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { SettingItem } from "./SettingItem";

export const AppSection = ({
  role = "client",
}: {
  role?: "client" | "vendor";
}) => {
  const { t } = useLanguage();
  const router = useRouter();

  const route = (path: string) => `/(${role})/(settings)/${path}` as Href;
  const { currentTheme } = useTheme();
  return (
    <View style={styles.section}>
      <View style={styles.settingsList}>
        <SettingItem
          title={t("settings.sections.app.language.title")}
          onPress={() => router.navigate(route("language"))}
          icon="globe"
        />
        <SettingItem
          title={t("settings.sections.app.theme.title")}
          onPress={() => router.navigate(route("theme"))}
          icon="palette"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginVertical: scale(12),
  },
  sectionTitle: {
    marginBottom: scale(6),
    opacity: 0.8,
  },
  settingsList: {
    gap: scale(8),
  },
});
