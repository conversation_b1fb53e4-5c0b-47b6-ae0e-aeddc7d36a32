import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useCart } from "@/src/hooks/useCart";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import { ThemedText } from "../ui/ThemedText";

import { CartDetailsBottomSheetProps } from "@/src/types";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function CartDetailsBottomSheet({
  isVisible,
  onClose,
}: CartDetailsBottomSheetProps) {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const {
    cartItems,
    increaseQuantity,
    decreaseQuantity,
    removeFromCart,
    clearCart,
    subtotal,
    total,
    totalItems,
    shipping,
    isEmpty,
  } = useCart();

  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = event.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, 0);
    })
    .onEnd((event) => {
      if (event.translationY > 50) {
        translateY.value = withSpring(SCREEN_HEIGHT, {
          damping: 50,
        });
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0, {
          damping: 50,
        });
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    const opacity = isVisible ? 1 - translateY.value / SCREEN_HEIGHT : 0;
    return {
      opacity: opacity * 0.4,
    };
  });

  useEffect(() => {
    if (isVisible) {
      translateY.value = withSpring(0, {
        damping: 50,
      });
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT, {
        damping: 50,
      });
    }
  }, [isVisible]);

  // Auto-close bottom sheet when cart becomes empty
  useEffect(() => {
    console.log(
      "🛒 CartDetailsBottomSheet: isEmpty =",
      isEmpty,
      "isVisible =",
      isVisible,
      "totalItems =",
      totalItems
    );
    if (isVisible && isEmpty) {
      console.log("🛒 Cart is empty, auto-closing bottom sheet");
      handleClose();
    }
  }, [isEmpty, isVisible, totalItems]);

  const handleClose = () => {
    translateY.value = withSpring(SCREEN_HEIGHT, {
      damping: 50,
    });
    setTimeout(onClose, 300);
  };

  const handleClearCart = () => {
    clearCart();
    handleClose();
  };

  const handleGoToCart = () => {
    handleClose();
    router.navigate("/(client)/(tabs)/shopcard");
  };

  const renderCartItem = ({ item }: { item: any }) => (
    <View style={styles.cartItem}>
      <TouchableOpacity
        onPress={() => {
          handleClose();
          router.navigate(`/(client)/(screens)/products/${item.id}/product`);
        }}
        style={styles.itemContent}
      >
        <Image source={{ uri: item.image }} style={styles.itemImage} />
        <View style={styles.itemDetails}>
          <ThemedText type="semi-bold" size={14} numberOfLines={2}>
            {item.name}
          </ThemedText>
          <ThemedText
            size={16}
            type="bold"
            style={{ color: Colors[currentTheme ?? "dark"].primary }}
          >
            {(item.price * item.quantity).toFixed(2)} TND
          </ThemedText>
        </View>
      </TouchableOpacity>

      <View style={styles.quantityControls}>
        <TouchableOpacity
          onPress={() => decreaseQuantity(item.id)}
          style={styles.quantityButton}
        >
          <MaterialIcons
            name="remove"
            size={18}
            color={Colors[currentTheme ?? "dark"].text}
          />
        </TouchableOpacity>
        <ThemedText style={styles.quantityText}>{item.quantity}</ThemedText>
        <TouchableOpacity
          onPress={() => increaseQuantity(item.id)}
          style={styles.quantityButton}
        >
          <MaterialIcons
            name="add"
            size={18}
            color={Colors[currentTheme ?? "dark"].text}
          />
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        onPress={() => removeFromCart(item.id)}
        style={styles.removeButton}
      >
        <MaterialIcons
          name="delete-outline"
          size={20}
          color={Colors[currentTheme ?? "dark"].error || "#FF3B30"}
        />
      </TouchableOpacity>
    </View>
  );

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        {/* Overlay */}
        <Animated.View
          style={[
            styles.overlay,
            overlayStyle,
            { backgroundColor: Colors[currentTheme ?? "dark"].overlay },
          ]}
        >
          <Pressable
            style={StyleSheet.absoluteFillObject}
            onPress={handleClose}
          />
        </Animated.View>

        {/* Bottom Sheet */}
        <GestureDetector gesture={gesture}>
          <Animated.View
            style={[
              styles.bottomsheet_container,
              reanimatedBottomStyle,
              {
                backgroundColor: Colors[currentTheme ?? "dark"].background,
              },
            ]}
          >
            <View
              style={[
                styles.line,
                { backgroundColor: Colors[currentTheme ?? "dark"].separator },
              ]}
            />

            {/* Header */}
            <View style={styles.header}>
              <ThemedText type="bold" size={20}>
                {t("cart.title")} ({totalItems}{" "}
                {totalItems === 1 ? t("cart.item") : t("cart.items")})
              </ThemedText>
              <TouchableOpacity
                onPress={handleClearCart}
                style={styles.clearButton}
              >
                <ThemedText
                  size={14}
                  type="bold"
                  style={{
                    color: Colors[currentTheme ?? "dark"].error,
                  }}
                >
                  {t("cart.clearAll")}
                </ThemedText>
              </TouchableOpacity>
            </View>

            {/* Cart Items */}
            <View style={styles.listContainer}>
              <FlatList
                data={cartItems}
                keyExtractor={(item) => item.id}
                renderItem={renderCartItem}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContent}
              />
            </View>

            {/* Summary */}
            <View style={styles.summary}>
              <View style={styles.summaryRow}>
                <ThemedText size={14}>{t("cart.subtotal")}</ThemedText>
                <ThemedText size={14}>{subtotal.toFixed(2)} TND</ThemedText>
              </View>
              <View style={styles.summaryRow}>
                <ThemedText size={14}>{t("cart.shipping")}</ThemedText>
                <ThemedText size={14}>
                  {shipping === 0
                    ? t("cart.free")
                    : `${shipping.toFixed(2)} TND`}
                </ThemedText>
              </View>
              <View style={[styles.summaryRow, styles.totalRow]}>
                <ThemedText type="bold" size={16}>
                  {t("cart.total")}
                </ThemedText>
                <ThemedText type="bold" size={16}>
                  {total.toFixed(2)} TND
                </ThemedText>
              </View>
            </View>

            {/* Actions */}
            <View style={styles.actions}>
              <DefaultButton
                title={t("cart.viewFullCart")}
                onPress={handleGoToCart}
                color={Colors[currentTheme ?? "dark"].primary}
                style={styles.actionButton}
              />
            </View>

            <Pressable onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={scaleFont(24)} color="black" />
            </Pressable>
          </Animated.View>
        </GestureDetector>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bottomsheet_container: {
    width: "100%",
    height: SCREEN_HEIGHT * 0.8,
    position: "absolute",
    bottom: 0,
    zIndex: 999,
    borderTopEndRadius: scale(25),
    borderTopStartRadius: scale(25),
    paddingHorizontal: scale(20),
    paddingBottom: scale(20),
  },
  line: {
    width: scale(75),
    height: scale(4),
    borderRadius: 20,
    alignSelf: "center",
    marginVertical: scale(10),
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: scale(20),
    paddingHorizontal: scale(5),
  },
  clearButton: {
    padding: scale(5),
  },
  listContainer: {
    flex: 1,
    marginBottom: scale(20),
  },
  listContent: {
    paddingBottom: scale(10),
  },
  cartItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(12),
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  itemContent: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  itemImage: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(8),
    marginRight: scale(12),
    backgroundColor: "#F5F5DC",
  },
  itemDetails: {
    flex: 1,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: scale(10),
  },
  quantityButton: {
    width: scale(28),
    height: scale(28),
    borderRadius: scale(14),
    backgroundColor: "rgba(0,0,0,0.1)",
    justifyContent: "center",
    alignItems: "center",
  },
  quantityText: {
    marginHorizontal: scale(12),
    minWidth: scale(20),
    textAlign: "center",
  },
  removeButton: {
    padding: scale(8),
  },
  summary: {
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
    paddingTop: scale(15),
    marginBottom: scale(20),
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: scale(8),
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
    paddingTop: scale(8),
    marginTop: scale(8),
  },
  actions: {
    gap: scale(12),
  },
  actionButton: {
    borderRadius: scale(12),
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(20),
    height: scale(40),
    width: scale(40),
    backgroundColor: "white",
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
