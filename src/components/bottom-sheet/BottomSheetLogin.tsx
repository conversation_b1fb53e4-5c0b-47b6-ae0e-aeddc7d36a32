import { scale, scaleFont } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { BottomSheetLoginProps } from "@/src/types";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useEffect } from "react";
import {
  Dimensions,
  Keyboard,
  Pressable,
  StyleSheet,
  View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import DefaultButton from "../buttons/Default";
import { ThemedText } from "../ui/ThemedText";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export default function BottomSheetLogin({
  isVisible,
  onClose,
  type,
}: BottomSheetLoginProps) {
  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });
  const { t } = useLanguage();
  const { currentTheme } = useTheme();

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((e) => {
      translateY.value = e.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, -SCREEN_HEIGHT / 3);
      translateY.value = Math.min(translateY.value, 0);
    })
    .onEnd(() => {
      if (translateY.value > -SCREEN_HEIGHT / 6) {
        translateY.value = withSpring(SCREEN_HEIGHT); // Hide the sheet completely
        runOnJS(onClose)();
      } else {
        translateY.value = withSpring(0); // Keep the sheet visible at the bottom
      }
    });

  const reanimatedBottomStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    return {
      opacity: isVisible ? 0.4 : 0,
    };
  });
  useEffect(() => {
    if (isVisible) {
      Keyboard.dismiss();
      translateY.value = withSpring(0); // Animate to the bottom
    } else {
      translateY.value = withSpring(SCREEN_HEIGHT); // Move off-screen
    }
  }, [isVisible]);

  const router = useRouter();
  return (
    <>
      <Animated.View
        style={[
          styles.overlay,
          overlayStyle,
          { backgroundColor: Colors[currentTheme ?? "dark"].overlay },
        ]}
        pointerEvents={isVisible ? "auto" : "none"}
      />
      <GestureDetector gesture={gesture}>
        <Animated.View
          style={[
            styles.bottomsheet_container,
            reanimatedBottomStyle,
            {
              backgroundColor: Colors[currentTheme ?? "dark"].background,
            },
          ]}
        >
          <View
            style={[
              styles.line,
              { backgroundColor: Colors[currentTheme ?? "dark"].separator },
            ]}
          />
          <View style={styles.container}>
            <View style={styles.content}>
              <ThemedText
                type="bold"
                size={24}
                style={{ marginBottom: scale(20) }}
              >
                {t("bottomSheets.login.title")}
              </ThemedText>
              <DefaultButton
                title={t("auth.userTypes.client")}
                onPress={() => {
                  if (type === "signin") {
                    router.navigate({
                      pathname: `/(user-entry)/signin`,
                      params: { type: "client" },
                    });
                    onClose();
                  } else {
                    router.navigate({
                      pathname: `/(user-entry)/signup`,
                      params: { type: "client" },
                    });
                    onClose();
                  }
                }}
              />
              <DefaultButton
                title={t("auth.userTypes.vendor")}
                color={Colors[currentTheme ?? "dark"].thirdary}
                active
                onPress={() => {
                  if (type === "signin") {
                    router.navigate({
                      pathname: `/(user-entry)/signin`,
                      params: { type: "vendor" },
                    });
                    onClose();
                  } else {
                    router.navigate({
                      pathname: `/(user-entry)/signup`,
                      params: { type: "vendor" },
                    });
                    onClose();
                  }
                }}
              />
            </View>
          </View>
          <Pressable onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={scaleFont(24)} />
          </Pressable>
        </Animated.View>
      </GestureDetector>
    </>
  );
}

const styles = StyleSheet.create({
  bottomsheet_container: {
    width: "100%",
    height:
      SCREEN_HEIGHT < scale(500) ? SCREEN_HEIGHT / 2.2 : SCREEN_HEIGHT / 3, // Define a fixed height
    position: "absolute",
    bottom: 0, // Keep it at the bottom
    zIndex: 999,
    borderTopEndRadius: scale(25),
    borderTopStartRadius: scale(25),
    paddingHorizontal: scale(5),
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  line: {
    width: scale(75),
    height: scale(4),
    borderRadius: 20,
    alignSelf: "center",
    marginVertical: scale(10),
  },
  container: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    width: "90%",
    justifyContent: "center",
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: scale(-50),
    right: scale(10),
    height: scale(40),
    width: scale(40),
    backgroundColor: "white",
    borderRadius: scale(20),
    justifyContent: "center",
    alignItems: "center",
  },
});
