import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { ThemedText } from "@/src/components/ui/ThemedText";

import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetProducts from "@/src/services/querys/client/useGetProducts";
import { IClientProduct } from "@/src/types";
import { router } from "expo-router";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonProductCard from "../../loader/FavoriteProductSkelaton";
import EmptyFavoriteProductsList from "../../ui/EmptyFavoriteProductsList";
import { ErrorComponentSmall } from "../../ui/ErrorComponentSmall";
import ProductsListItem from "./ProductsListItem";

export default function ProductSlider() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetProducts();
  const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0;

  const [refreshing, setRefreshing] = useState(false);

  const products = useMemo(() => {
    // Add null/undefined checks for data and pages

    return data?.pages.flatMap((page) => {
      // Add null/undefined check for page data
      if (!page?.data?.items) {
        return [];
      }

      return page.data.items
        .filter((product: IClientProduct) => product && product._id) // Filter out invalid items
        .map((product: IClientProduct) => ({
          id: product._id,
          title: product.name || "",
          price: product.price || "",
          image: { uri: product.images?.[0] || "" },
        }));
    });
  }, [data]);
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const renderProductItem = ({ item }: any) => <ProductsListItem {...item} />;
  const handleViewAll = () => {
    router.navigate("/(client)/(screens)/products-list");
  };
  // Safe key extractor with fallback
  const keyExtractor = (item: any, index: number) => {
    return item?.id?.toString() || `product-${index}`;
  };

  if (error) {
    return (
      <>
        <View style={styles.header}>
          <ThemedText size={18} type="bold">
            {t("client.sliders.featuredProducts")} ({totalItems})
          </ThemedText>
          <ThemedText
            style={{ color: Colors[currentTheme ?? "dark"].secondary }}
            onPress={handleViewAll}
          >
            {t("client.sliders.viewAll")}
          </ThemedText>
        </View>
        <ErrorComponentSmall />
      </>
    );
  }

  return (
    <View>
      <View style={styles.header}>
        <ThemedText size={18} type="bold">
          {t("client.sliders.featuredProducts")} ({totalItems})
        </ThemedText>
        <ThemedText
          style={{ color: Colors[currentTheme ?? "dark"].secondary }}
          onPress={handleViewAll}
        >
          {t("client.sliders.viewAll")}
        </ThemedText>
      </View>
      <InfiniteScrollList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={keyExtractor}
        onEndReached={fetchNextPage}
        onEndReachedThreshold={0.1}
        estimatedItemSize={200}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        skeletonComponent={<SkeletonProductCard />}
        emptyComponent={<EmptyFavoriteProductsList />}
        count={6}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingRight: scale(10) }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: scale(10),
    marginTop: scale(10),
  },
});
