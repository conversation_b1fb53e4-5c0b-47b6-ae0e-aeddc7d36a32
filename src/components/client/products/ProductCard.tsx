import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ImageProps,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ThemedText } from "../../ui/ThemedText";

const ProductCard = ({
  image,
  title,
  subtitle,
  id,
}: {
  image: ImageProps | null;
  title: string;
  subtitle: string;
  id: string;
}) => {
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";

  return (
    <TouchableOpacity
      style={[styles.container]}
      onPress={() =>
        router.navigate(`/(client)/(screens)/products/${id}/product`)
      }
    >
      <View
        style={{
          backgroundColor: Colors[theme].secondary,
          justifyContent: "center",
          alignItems: "center",
          borderRadius: scale(13),
        }}
      >
        <Image
          source={image || require("@/src/assets/images/icon.png")}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      <View style={{ marginTop: scale(8), paddingHorizontal: scale(7) }}>
        <ThemedText
          size={16}
          type="semi-bold"
          style={styles.title}
          numberOfLines={2}
        >
          {title}
        </ThemedText>

        <ThemedText
          numberOfLines={2}
          size={14}
          type="regular"
          style={{
            color: Colors[theme].secondary,
            marginBottom: scale(8),
          }}
        >
          {subtitle}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: scale(160),
    borderRadius: scale(16),
    marginRight: scale(15),
  },
  image: {
    width: scale(160),
    height: scale(160),
    borderRadius: scale(12),
  },
  title: {
    marginBottom: scale(4),
  },
});

export default ProductCard;
