// FavoriteProducts.tsx
import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { ThemedText } from "@/src/components/ui/ThemedText";

import { useLanguage } from "@/src/context/LanguageContext";
import useGetFavoriteProducts, {
  IProduct,
} from "@/src/services/querys/client/useGetFavoriteProducts";
import { router } from "expo-router";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonProductCard from "../../loader/FavoriteProductSkelaton";
import EmptyFavoriteProductsList from "../../ui/EmptyFavoriteProductsList";
import { ErrorComponentSmall } from "../../ui/ErrorComponentSmall";
import ProductCard from "./ProductCard";

export default function FavoriteProducts() {
  const { t } = useLanguage();
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetFavoriteProducts();
  const totalItems = data?.pages?.[0]?.data?.totalItems ?? 0;

  const [refreshing, setRefreshing] = useState(false);

  const favoriteProducts = useMemo(() => {
    return data?.pages.flatMap((page) => {
      // Add null/undefined check for page data
      if (!page?.data?.items) {
        return [];
      }

      return page.data.items
        .filter((product: IProduct) => product && product._id) // Filter out invalid items
        .map((product: IProduct) => ({
          id: product._id,
          title: product.name || "",
          brand: product.brand?.name || "",
          image: { uri: product.images?.[0] || "" },
        }));
    });
  }, [data]);
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleProductPress = (id: string) => {
    if (id) {
      router.navigate(`/(client)/(screens)/products/${id}/product`);
    }
  };

  const renderFavoriteProductItem = ({ item }: any) => (
    <ProductCard
      {...item}
      onPressViewAll={() => handleProductPress(item?.id)}
    />
  );

  // Safe key extractor with fallback
  const keyExtractor = (item: any, index: number) => {
    return item?.id?.toString() || `product-${index}`;
  };

  if (error) {
    return (
      <>
        <View style={styles.header}>
          <ThemedText size={18} type="bold">
            {t("client.favorites.products")} ({totalItems})
          </ThemedText>
        </View>
        <ErrorComponentSmall />
      </>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ThemedText size={18} type="bold">
          {t("client.favorites.products")} ({totalItems})
        </ThemedText>
      </View>
      <InfiniteScrollList
        data={favoriteProducts}
        renderItem={renderFavoriteProductItem}
        keyExtractor={keyExtractor}
        onEndReached={fetchNextPage}
        estimatedItemSize={30}
        onEndReachedThreshold={0.1}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        skeletonComponent={<SkeletonProductCard />}
        emptyComponent={<EmptyFavoriteProductsList />}
        count={6}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingRight: scale(10) }}
        numColumns={2}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: scale(10),
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: scale(10),
  },
});
