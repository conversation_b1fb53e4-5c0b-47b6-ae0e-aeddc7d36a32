import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { useCart } from "@/src/hooks/useCart";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useEffect, useState } from "react";
import {
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

import { SelectedLocationData } from "@/src/types";
import useCreateOrderMutation from "../../../services/querys/client/useCreateOrder";
import { ILocation } from "../../../types/location";
import AddressBottomSheet from "../../bottom-sheet/AddressBottomSheet";
import AddressTypeSelector from "../../bottom-sheet/AddressTypeSelector";
import LocationPermissionModel from "../../bottom-sheet/LocationPermissionModel";
import MapLocationPicker from "../../bottom-sheet/MapLocationPicker";
import SwipeToRightButton from "../../buttons/SwipeToRightButton";
import Loader from "../../loader/Loader";
import { ThemedText } from "../../ui/ThemedText";
import { EmptyCart } from "./EmptyCart";

export function CartWithItems() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const {
    cartItems,
    increaseQuantity,
    decreaseQuantity,
    removeFromCart,
    subtotal,
    total,
    shipping,
    isLoading,
    clearCart,
  } = useCart();

  // Order creation mutation
  const createOrderMutation = useCreateOrderMutation();

  // State for bottom sheets
  const [isAddressBottomSheetVisible, setIsAddressBottomSheetVisible] =
    useState(false);
  const [showMapPicker, setShowMapPicker] = useState(false);
  const [showAddressTypeSelector, setShowAddressTypeSelector] = useState(false);
  const [showLocationPermission, setShowLocationPermission] = useState(false);

  // State for selected location data
  const [selectedLocationData, setSelectedLocationData] =
    useState<SelectedLocationData | null>(null);

  // State for order creation loading

  // Debug effect to track when AddressTypeSelector becomes visible
  useEffect(() => {
    if (showAddressTypeSelector) {
      console.log(
        "🏠 AddressTypeSelector is now visible, selectedLocationData:",
        selectedLocationData
      );
    }
  }, [showAddressTypeSelector, selectedLocationData]);

  // Handler functions for location-related logic
  const handleAddNewAddress = () => {
    // Close address sheet and open map picker
    setIsAddressBottomSheetVisible(false);
    setShowMapPicker(true);
  };

  const handleMapLocationSelect = (locationData: SelectedLocationData) => {
    // Store the selected location data and move to address type selection
    console.log("🗺️ Map location selected:", locationData);
    console.log(
      "🔄 Current selectedLocationData before update:",
      selectedLocationData
    );

    setSelectedLocationData(locationData);
    console.log("✅ Called setSelectedLocationData with:", locationData);

    setShowMapPicker(false);

    // Use setTimeout to ensure state is updated before opening AddressTypeSelector
    setTimeout(() => {
      console.log("📍 Opening AddressTypeSelector after timeout");
      setShowAddressTypeSelector(true);
    }, 100);
  };

  const handleCloseMapPicker = () => {
    console.log(
      "🚪 handleCloseMapPicker called - NOT clearing selectedLocationData"
    );
    setShowMapPicker(false);
    // Don't clear selectedLocationData here - it's needed for AddressTypeSelector
  };

  const handleCloseAddressTypeSelector = () => {
    console.log(
      "🚪 handleCloseAddressTypeSelector called - clearing selectedLocationData"
    );
    setShowAddressTypeSelector(false);
    setSelectedLocationData(null);
  };

  const handleCloseLocationPermission = () => {
    setShowLocationPermission(false);
  };

  // Function to create order with selected address
  const handleCreateOrder = async (selectedAddress: ILocation) => {
    if (createOrderMutation.isPending) return; // Prevent multiple simultaneous orders

    try {
      // Create products array with quantities
      const products = cartItems.map((item) => ({
        product: item.id, // Just send the product ID string
        quantity: item.quantity,
      }));

      if (cartItems.length === 0) {
        console.error("No items in cart");
        return;
      }

      // Get brand ID from the first product
      const brandId = cartItems[0].brandId;

      if (!brandId) {
        console.error("Could not get brand ID from product");
        // You might want to show a toast error here
        return;
      }

      // Create the order
      await createOrderMutation.mutateAsync({
        products: products,
        brandId: brandId,
        locationId: selectedAddress._id || "",
        totalPrice: total,
      });

      console.log("Order created successfully!");
      // Clear the cart after successful order creation
      clearCart();

      // Small delay to ensure UI updates
      setTimeout(() => {
        console.log("🛒 Cart should now be empty");
      }, 100);
    } catch (error) {
      console.error("Error creating order:", error);
      // Error handling is already done in the mutation's onError callback
    }
  };
  if (isLoading) {
    return <Loader />;
  }
  if (cartItems.length === 0) {
    return <EmptyCart />;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={cartItems}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={() => {
              router.navigate(
                `/(client)/(screens)/products/${item.id}/product`
              );
            }}
            style={styles.row}
          >
            <Image
              source={{ uri: item.image }}
              style={[
                styles.image,
                {
                  backgroundColor: Colors[currentTheme ?? "dark"].secondary,
                },
              ]}
            />
            <View style={styles.itemDetails}>
              <ThemedText
                size={16}
                type="semi-bold"
                numberOfLines={1}
                ellipsizeMode="clip"
              >
                {item.name}
              </ThemedText>
              <View style={styles.quantityControls}>
                <TouchableOpacity
                  onPress={() => decreaseQuantity(item.id)}
                  style={styles.quantityButton}
                >
                  <MaterialIcons
                    name="remove"
                    size={20}
                    color={Colors[currentTheme ?? "dark"].text}
                  />
                </TouchableOpacity>
                <ThemedText style={styles.quantityText}>
                  {item.quantity}
                </ThemedText>
                <TouchableOpacity
                  onPress={() => increaseQuantity(item.id)}
                  style={styles.quantityButton}
                >
                  <MaterialIcons
                    name="add"
                    size={20}
                    color={Colors[currentTheme ?? "dark"].text}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <View style={styles.priceAndRemove}>
              <ThemedText size={16} type="semi-bold">
                {(item.price * item.quantity).toFixed(2)} TND
              </ThemedText>
              <TouchableOpacity
                onPress={() => removeFromCart(item.id)}
                style={styles.removeButton}
              >
                <MaterialIcons
                  name="delete"
                  size={20}
                  color={Colors[currentTheme ?? "dark"].error || "#ff4444"}
                />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        )}
        showsVerticalScrollIndicator={false}
      />

      <View style={styles.footer}>
        <View style={styles.summary}>
          <Row
            label={t("cart.labels.subtotal")}
            value={`${subtotal.toFixed(2)} TND`}
          />
          <Row
            label={t("cart.labels.shipping")}
            value={
              shipping === 0
                ? t("cart.labels.free")
                : `${shipping.toFixed(2)} TND`
            }
          />
          <Row
            label={t("cart.labels.total")}
            value={`${total.toFixed(2)} TND`}
            highlight
          />
        </View>
        <SwipeToRightButton
          title={
            createOrderMutation.isPending
              ? t("cart.actions.processing")
              : t("cart.actions.checkout")
          }
          onSwipeComplete={() => {
            if (!createOrderMutation.isPending) {
              setIsAddressBottomSheetVisible(true);
            }
          }}
          disabled={createOrderMutation.isPending}
        />
      </View>

      <AddressBottomSheet
        isVisible={isAddressBottomSheetVisible}
        onClose={() => setIsAddressBottomSheetVisible(false)}
        onAddressSelect={(address: ILocation) => {
          console.log("Selected address:", address);
          setIsAddressBottomSheetVisible(false);
          // Create order with selected address
          handleCreateOrder(address);
        }}
        onAddNewAddress={handleAddNewAddress}
      />

      {/* Map Location Picker */}
      <MapLocationPicker
        isVisible={showMapPicker}
        onClose={handleCloseMapPicker}
        onLocationSelect={handleMapLocationSelect}
      />

      {/* Address Type Selector */}
      <AddressTypeSelector
        isVisible={showAddressTypeSelector}
        onClose={handleCloseAddressTypeSelector}
        selectedLocation={selectedLocationData}
      />

      {/* Location Permission Bottom Sheet */}
      <LocationPermissionModel
        isVisible={showLocationPermission}
        onClose={handleCloseLocationPermission}
      />
    </View>
  );
}

function Row({
  label,
  value,
  highlight,
}: {
  label: string;
  value: string;
  highlight?: boolean;
}) {
  const { currentTheme } = useTheme();
  return (
    <View style={styles.rowSummary}>
      <ThemedText
        type="semi-bold"
        style={{
          color: highlight
            ? Colors[currentTheme ?? "dark"].text
            : Colors[currentTheme ?? "dark"].secondary,
        }}
      >
        {label}
      </ThemedText>
      <ThemedText
        type="semi-bold"
        style={{
          color: highlight
            ? Colors[currentTheme ?? "dark"].text
            : Colors[currentTheme ?? "dark"].text,
        }}
      >
        {value}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "space-between",
  },
  listContent: {
    paddingBottom: scale(20),
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: scale(16),
  },
  image: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(8),
    marginRight: scale(12),
  },
  itemDetails: {
    flex: 1,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: scale(8),
  },
  quantityButton: {
    width: scale(32),
    height: scale(32),
    borderRadius: scale(16),
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: scale(4),
  },
  quantityText: {
    marginHorizontal: scale(12),
    minWidth: scale(30),
    textAlign: "center",
  },
  priceAndRemove: {
    alignItems: "flex-end",
  },
  removeButton: {
    marginTop: scale(8),
    padding: scale(4),
  },
  summary: {
    marginBottom: scale(12),
  },
  rowSummary: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: scale(6),
  },
  footer: {
    paddingTop: scale(10),
  },
});
