// components/cart/EmptyCart.tsx
import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import { Image, View } from "react-native";
import DefaultButton from "../../buttons/Default";
import { ThemedText } from "../../ui/ThemedText";

export function EmptyCart() {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();

  return (
    <View
      style={{
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Image
        source={require("@/src/assets/ui/empty_cart.png")} // replace with your cart image
        style={{ width: "100%", borderRadius: scale(12) }}
        resizeMode="contain"
      />
      <ThemedText type="bold" size={18} style={{ marginTop: scale(16) }}>
        {t("client.cart.empty.title")}
      </ThemedText>
      <ThemedText style={{ textAlign: "center", marginTop: scale(8) }}>
        {t("client.cart.empty.description")}
      </ThemedText>
      <DefaultButton
        onPress={() => {
          router.navigate("/(client)/(screens)/products-list");
        }}
        title={t("buttons.startShopping")}
        active
        style={{
          backgroundColor: Colors[currentTheme ?? "dark"].thirdary,
          marginTop: scale(16),
        }}
      />
    </View>
  );
}
