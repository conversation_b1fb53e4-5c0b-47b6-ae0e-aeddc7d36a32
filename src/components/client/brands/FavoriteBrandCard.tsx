import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import React from "react";
import {
  Image,
  ImageProps,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ThemedText } from "../../ui/ThemedText";

const FavoriteBrandCard = ({
  image,
  title,
  onPress,
}: {
  image: ImageProps | null;
  title: string;
  onPress?: () => void;
}) => {
  const { currentTheme } = useTheme();

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.7}
      onPress={onPress}
    >
      <View
        style={{
          backgroundColor: Colors[currentTheme ?? "dark"].secondary,
          justifyContent: "center",
          alignItems: "center",
          borderRadius: scale(100),
        }}
      >
        <Image
          source={image || require("@/src/assets/images/icon.png")}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      <View style={{ marginTop: scale(8), paddingHorizontal: scale(7) }}>
        <ThemedText
          size={16}
          type="semi-bold"
          style={styles.title}
          numberOfLines={1}
        >
          {title}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: scale(160),
    alignItems: "center",
  },
  image: {
    width: scale(128),
    height: scale(128),
    borderRadius: scale(64),
  },
  title: {
    marginBottom: scale(4),
  },
});

export default FavoriteBrandCard;
