import { scale, scaleFont } from "@/src/_helper/Scaler";
import DefaultButton from "@/src/components/buttons/Default";
import Loader from "@/src/components/loader/Loader";
import { DeleteConfirmationModal } from "@/src/components/model/DeleteConfirmationModal";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { ThemedView } from "@/src/components/ui/ThemedView";
import { AppTheme, Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useDeleteOrderMutation from "@/src/services/querys/client/useDeleteOrder";
import useGetOrderDetails from "@/src/services/querys/client/useGetOrderDetails";
import { MaterialIcons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

const OrderDetails = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const router = useRouter();
  const { id } = useLocalSearchParams() as { id: string };
  const theme = currentTheme ?? "dark";
  const { data, refetch, isError, error, isLoading } = useGetOrderDetails(id);
  const deleteOrderMutation = useDeleteOrderMutation();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const handleRefetch = async () => await refetch();
  const styles = getStyles(theme);

  // Loading state
  if (isLoading) {
    return <Loader />;
  }

  // Error state
  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  const order = data?.data?.order;

  if (!order) {
    return (
      <ErrorComponent
        error={t("errors.orderNotFound")}
        onRetry={handleRefetch}
      />
    );
  }

  const getStatusColor = (status?: string) => {
    if (!status) return Colors[theme].text;

    switch (status.toLowerCase()) {
      case "pending":
        return Colors[theme].orange;
      case "confirmed":
        return Colors[theme].green;
      case "accepted":
        return Colors[theme].blue;
      case "delivered":
        return Colors[theme].green;
      case "cancelled":
        return Colors[theme].error;
      default:
        return Colors[theme].text;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatPrice = (price?: number) => {
    if (!price) return "0.00 TND";
    return `${price.toFixed(2)} TND`;
  };

  const handleCancelPress = () => {
    setShowCancelModal(true);
  };

  const handleCancelConfirm = async () => {
    try {
      await deleteOrderMutation.mutateAsync({ id });
      setShowCancelModal(false);
      router.back();
    } catch (error) {
      console.error("Cancel error:", error);
    }
  };

  const handleCancelCancel = () => {
    setShowCancelModal(false);
  };
  console.log(order.products?.[0]?.product?._id);
  const handleProductPress = (productId: string) => {
    if (productId) {
      router.navigate(`/(client)/(screens)/products/${productId}/product`);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <ThemedView style={styles.section}>
        <ThemedText type="bold" size={18} style={styles.sectionTitle}>
          {order.products && order.products.length > 1
            ? t("orderDetails.sections.products")
            : t("orderDetails.sections.product")}
        </ThemedText>

        {order.products?.map((productItem: any, index: number) => (
          <TouchableOpacity
            key={productItem.product?._id || index}
            style={[
              styles.productContainer,
              index > 0 && styles.productContainerSpacing,
            ]}
            onPress={() => handleProductPress(productItem.product?._id || "")}
            activeOpacity={0.7}
          >
            <View
              style={{
                flexDirection: "column",
                alignItems: "center",
                marginRight: scale(16),
                gap: scale(8),
              }}
            >
              <View style={styles.quantityBadge}>
                <ThemedText
                  size={12}
                  type="semi-bold"
                  style={styles.quantityText}
                >
                  x{productItem.quantity || 1}
                </ThemedText>
              </View>
              {productItem.product?.images?.[0] ? (
                <Image
                  source={{ uri: productItem.product.images[0] }}
                  style={styles.productImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.placeholderImage}>
                  <MaterialIcons
                    name="shopping-bag"
                    size={scaleFont(40)}
                    color={Colors[theme].text}
                  />
                </View>
              )}
            </View>
            <View style={styles.productInfo}>
              <View style={styles.productHeader}>
                <ThemedText type="semi-bold" size={16}>
                  {productItem.product?.name ||
                    t("orderDetails.fallbacks.unknownProduct")}
                </ThemedText>
              </View>
              <ThemedText size={14} style={styles.brandText}>
                {order.brand?.name || t("orderDetails.fallbacks.unknownBrand")}
              </ThemedText>
              <View style={styles.priceContainer}>
                <ThemedText type="semi-bold" size={16} style={styles.priceText}>
                  {formatPrice(productItem.product?.price || 0)}
                </ThemedText>
                <ThemedText size={12} style={styles.unitPriceText}>
                  {t("orderDetails.labels.unitPrice")}
                </ThemedText>
              </View>
              {productItem.quantity && productItem.quantity > 1 && (
                <ThemedText
                  type="semi-bold"
                  size={14}
                  style={styles.totalItemPrice}
                >
                  {t("orderDetails.labels.totalForItem")}:{" "}
                  {formatPrice(
                    (productItem.product?.price || 0) * productItem.quantity
                  )}
                </ThemedText>
              )}
            </View>

            <MaterialIcons
              name="chevron-right"
              size={scaleFont(24)}
              color={Colors[theme].text}
              style={styles.chevron}
            />
          </TouchableOpacity>
        ))}
      </ThemedView>

      {/* Customer Section */}
      <ThemedView style={styles.section}>
        <ThemedText type="bold" size={18} style={styles.sectionTitle}>
          {t("orderDetails.sections.customer")}
        </ThemedText>

        <View style={styles.infoRow}>
          <MaterialIcons
            name="person"
            size={scaleFont(20)}
            color={Colors[theme].text}
            style={styles.icon}
          />
          <View>
            <ThemedText size={14} style={styles.label}>
              {t("orderDetails.labels.customerId")}
            </ThemedText>
            <ThemedText type="semi-bold" size={16}>
              {order.client?.name ||
                t("orderDetails.fallbacks.unknownCustomer")}
            </ThemedText>
          </View>
        </View>
      </ThemedView>

      {/* Delivery Section */}
      <ThemedView style={styles.section}>
        <ThemedText type="bold" size={18} style={styles.sectionTitle}>
          {t("orderDetails.sections.delivery")}
        </ThemedText>

        <View style={styles.infoRow}>
          <MaterialIcons
            name="location-on"
            size={scaleFont(20)}
            color={Colors[theme].text}
            style={styles.icon}
          />
          <View style={styles.flex}>
            <ThemedText size={14} style={styles.label}>
              {t("orderDetails.labels.address")}
            </ThemedText>
            <ThemedText type="semi-bold" size={16}>
              {order.location?.address ||
                t("orderDetails.fallbacks.addressNotProvided")}
            </ThemedText>
            <ThemedText size={14} style={styles.cityText}>
              {order.location?.city || t("orderDetails.fallbacks.notAvailable")}
            </ThemedText>
          </View>
        </View>
      </ThemedView>

      {/* Order Information Section */}
      <ThemedView style={styles.section}>
        <ThemedText type="bold" size={18} style={styles.sectionTitle}>
          {t("orderDetails.sections.orderInformation")}
        </ThemedText>

        <View style={styles.orderInfoContainer}>
          <View style={styles.infoRow}>
            <MaterialIcons
              name="calendar-today"
              size={scaleFont(20)}
              color={Colors[theme].text}
              style={styles.icon}
            />
            <View>
              <ThemedText size={14} style={styles.label}>
                {t("orderDetails.labels.orderDate")}
              </ThemedText>
              <ThemedText type="semi-bold" size={16}>
                {formatDate(order.orderDate)}
              </ThemedText>
            </View>
          </View>

          <View style={styles.infoRow}>
            <MaterialIcons
              name="info"
              size={scaleFont(20)}
              color={getStatusColor(order.orderStatus)}
              style={styles.icon}
            />
            <View>
              <ThemedText size={14} style={styles.label}>
                {t("orderDetails.labels.orderStatus")}
              </ThemedText>
              <ThemedText
                type="semi-bold"
                size={16}
                style={{ color: getStatusColor(order.orderStatus) }}
              >
                {order.orderStatus
                  ? t(`orderStatus.${order.orderStatus.toLowerCase()}`)
                  : "N/A"}
              </ThemedText>
            </View>
          </View>

          <View style={styles.infoRow}>
            <MaterialIcons
              name="attach-money"
              size={scaleFont(20)}
              color={Colors[theme].text}
              style={styles.icon}
            />
            <View>
              <ThemedText size={14} style={styles.label}>
                {t("orderDetails.labels.totalPrice")}
              </ThemedText>
              <ThemedText type="bold" size={18} style={styles.totalPrice}>
                {formatPrice(order.totalPrice)}
              </ThemedText>
            </View>
          </View>
        </View>
      </ThemedView>

      {order.orderStatus === "Pending" && (
        <View style={styles.cancelButtonContainer}>
          <DefaultButton
            title={t("orders.actions.cancel")}
            onPress={handleCancelPress}
            color="#F44336"
            style={styles.cancelButton}
          />
        </View>
      )}

      <DeleteConfirmationModal
        visible={showCancelModal}
        title={t("orders.modal.cancelTitle")}
        message={t("orders.modal.cancelMessage")}
        confirmText={t("orders.modal.cancelConfirm")}
        cancelText={t("orders.modal.cancelCancel")}
        onConfirm={handleCancelConfirm}
        onClose={handleCancelCancel}
        isLoading={deleteOrderMutation.isPending}
      />
    </ScrollView>
  );
};

const getStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      borderRadius: scale(12),
      marginBottom: scale(8),
    },
    sectionTitle: {
      marginBottom: scale(16),
    },
    productContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    productContainerSpacing: {
      marginTop: scale(16),
      paddingTop: scale(16),
      borderTopWidth: 1,
      borderTopColor: Colors[theme].text + "20",
    },
    productImage: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(8),
    },
    placeholderImage: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(8),
      backgroundColor: Colors[theme].primary + "20",
      alignItems: "center",
      justifyContent: "center",
    },
    productInfo: {
      flex: 1,
    },
    productHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: scale(4),
    },
    quantityBadge: {
      backgroundColor: Colors[theme].primary,
      height: scale(30),
      width: scale(30),
      borderRadius: scale(15),
      paddingHorizontal: scale(8),
      justifyContent: "center",
    },
    quantityText: {
      color: "white",
    },
    brandText: {
      opacity: 0.7,
      marginBottom: scale(4),
    },
    priceContainer: {
      flexDirection: "row",
      alignItems: "baseline",
      gap: scale(4),
      marginBottom: scale(4),
    },
    priceText: {
      color: Colors[theme].primary,
    },
    unitPriceText: {
      opacity: 0.6,
    },
    totalItemPrice: {
      color: Colors[theme].secondary,
      marginTop: scale(2),
    },
    infoRow: {
      flexDirection: "row",
      alignItems: "flex-start",
      marginBottom: scale(16),
    },
    icon: {
      marginRight: scale(12),
      marginTop: scale(2),
    },
    label: {
      opacity: 0.7,
      marginBottom: scale(4),
    },
    flex: {
      flex: 1,
    },
    cityText: {
      opacity: 0.6,
      marginTop: scale(2),
    },
    orderInfoContainer: {
      gap: scale(4),
    },
    totalPrice: {
      color: Colors[theme].primary,
    },
    chevron: {
      marginLeft: scale(8),
    },
    cancelButtonContainer: {
      padding: scale(16),
      paddingTop: scale(8),
    },
    cancelButton: {
      width: "100%",
      borderRadius: scale(12),
      paddingVertical: scale(16),
    },
  });

export default OrderDetails;
