import { scale } from "@/src/_helper/Scaler";
import { InfiniteScrollList } from "@/src/components/shared/InfiniteScrollList";
import { useLanguage } from "@/src/context/LanguageContext";
import useGetOrders, {
  IOrder,
} from "@/src/services/querys/vendor/useGetOrders";
import React, { useMemo, useState } from "react";
import { StyleSheet, View } from "react-native";
import SkeletonOrderList from "../../loader/SkelatonOrderList";
import EmptyOrderList from "../../ui/EmptyOrderList";
import { ErrorComponent } from "../../ui/ErrorComponent";
import OrderCard from "./OrderCard";
import OrdersHeader from "./OrderHeader";

const OrderListContainer = () => {
  const {
    data,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetOrders();

  const [refreshing, setRefreshing] = useState(false);
  const { t } = useLanguage();

  const orders = useMemo(() => {
    return (
      data?.pages.flatMap((page) =>
        page.data.items.map((order: IOrder) => ({
          id: order._id,
          name:
            order.client?.name || t("orderDetails.fallbacks.unknownCustomer"),
          phone:
            order.client?.phone || t("orderDetails.fallbacks.phoneNotProvided"),
          brand: order.brand?.name || t("orderDetails.fallbacks.unknownBrand"),
          product:
            order.products && order.products.length > 1
              ? t("orders.multipleProducts", { count: order.products.length })
              : order.products?.[0]?.product?.name ||
                t("orderDetails.fallbacks.unknownProduct"),
          status: order.orderStatus,
        }))
      ) || []
    );
  }, [data?.pages, t]);
  const handleRefetch = async () => await refetch();
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  if (error) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  return (
    <View style={styles.productsSection}>
      <OrdersHeader />
      <InfiniteScrollList
        data={orders}
        isLoading={isLoading}
        isFetchingNextPage={isFetchingNextPage}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        estimatedItemSize={82}
        renderItem={({ item }) => <OrderCard item={item} />}
        skeletonComponent={<SkeletonOrderList />}
        emptyComponent={<EmptyOrderList />}
        count={5}
      />
    </View>
  );
};

export default OrderListContainer;

const styles = StyleSheet.create({
  productsSection: {
    flex: 1,
  },
  fab: {
    position: "absolute",
    bottom: scale(30),
    right: scale(20),
    width: scale(56),
    height: scale(56),
    borderRadius: scale(28),
    justifyContent: "center",
    alignItems: "center",
    elevation: 8, // elevation should be an integer
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
});
