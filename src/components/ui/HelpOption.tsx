// components/help/HelpOption.tsx
import { scale, scaleFont } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import EvilIcons from "@expo/vector-icons/EvilIcons";
import { Href, router } from "expo-router";
import React from "react";
import { Pressable, StyleSheet } from "react-native";

interface HelpOptionProps {
  title: string;
  route: Href;
}

const HelpOption: React.FC<HelpOptionProps> = ({ title, route }) => {
  const { currentTheme } = useTheme();
  const themeColors = Colors[currentTheme ?? "dark"];

  return (
    <Pressable
      onPress={() => router.navigate(route)}
      style={({ pressed }) => [
        styles.option,
        {
          backgroundColor: pressed
            ? themeColors.background ?? "#f2f2f2"
            : "transparent",
        },
      ]}
    >
      <ThemedText size={16}>{title}</ThemedText>
      <EvilIcons
        name="chevron-right"
        size={scaleFont(24)}
        color={themeColors.text}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  option: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: scale(15),
    paddingVertical: scale(10),
  },
});

export default HelpOption;
