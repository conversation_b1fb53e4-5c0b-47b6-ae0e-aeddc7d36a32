import { scale } from "@/src/_helper/Scaler";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

interface ProductsHeaderProps {
  productCount?: number;
  onFilterPress?: () => void;
  hasActiveFilters?: boolean;
}

const ProductsHeader = ({
  productCount,
  onFilterPress,
  hasActiveFilters = false,
}: ProductsHeaderProps) => {
  const { t } = useLanguage();
  const { currentTheme } = useTheme();

  return (
    <View style={styles.header}>
      <View style={styles.titleContainer}>
        <ThemedText type="bold" size={22}>
          {productCount !== undefined
            ? `${t("vendor.headers.products")} (${productCount})`
            : t("vendor.headers.products")}
        </ThemedText>
        <ThemedText size={16}>
          {t("vendor.headers.productsSubtitle")}
        </ThemedText>
      </View>

      {onFilterPress && (
        <TouchableOpacity
          style={[
            styles.filterButton,
            {
              backgroundColor: hasActiveFilters
                ? Colors[currentTheme ?? "dark"].primary
                : Colors[currentTheme ?? "dark"].background,
              borderColor: Colors[currentTheme ?? "dark"].border,
            },
          ]}
          onPress={onFilterPress}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name="filter-list"
            size={24}
            color={
              hasActiveFilters ? "#fff" : Colors[currentTheme ?? "dark"].text
            }
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ProductsHeader;

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginTop: scale(20),
    marginBottom: scale(12),
  },
  titleContainer: {
    flex: 1,
  },
  filterButton: {
    width: scale(44),
    height: scale(44),
    borderRadius: scale(22),
    borderWidth: 1,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: scale(12),
  },
});
