import { IVendorProduct, MessagePage } from "@/src/types";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getVendorProducts } from "../../apis/vendor";

interface UseGetProductsParams {
  brandId?: string;
  categoryId?: string;
}

const useGetProducts = (params?: UseGetProductsParams) => {
  return useInfiniteQuery<MessagePage<IVendorProduct>, Error>({
    queryKey: ["vendor-products", params?.brandId, params?.categoryId],
    queryFn: async ({ pageParam }) => {
      return await getVendorProducts({
        page: pageParam as number,
        brandId: params?.brandId,
        categoryId: params?.categoryId,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.hasNextPage
        ? lastPage.data.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
  });
};
export default useGetProducts;
